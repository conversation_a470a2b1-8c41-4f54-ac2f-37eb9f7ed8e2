import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaFlag, FaEye, FaImage, FaFilePdf, FaFileWord } from "react-icons/fa";
import { FaUserGroup } from "react-icons/fa6";
import { HiDotsHorizontal } from "react-icons/hi";
import { HiUserCircle } from "react-icons/hi";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BiFilter } from "react-icons/bi";
import { MdKeyboardArrowDown } from "react-icons/md";
import { IoIosNotificationsOutline } from "react-icons/io";
import { AnnouncementActionMenu, AnnouncementHistoryModal, usePinPost, PinIcon } from "./components";
import Calendar from "./components/Calendar";
import ConfirmationMessageBox from "../../../Components/MessageBox/ConfirmationMessageBox";
import ImageSlider from "../../../Components/Modal/ImageSlider";
import DocumentViewer from "../../../Components/FileViewer/DocumentViewer";
import { addSampleAnnouncementToStorage } from "./utils/sampleData";
import { announcementApi } from "../../../api/announcementsApi/announcementsBackendApi";

//  Created By Firoj Hasan

// Priority color mapping - consistent with PriorityDropdown component
const PRIORITY_COLORS = {
    'urgent': 'text-red-500',
    'high': 'text-yellow-500',
    'normal': 'text-[#3D9D9B]',
    'low': 'text-gray-400'
};

const getPriorityColor = (priority) => {
    return PRIORITY_COLORS[priority?.toLowerCase()] || 'text-gray-500';
};

// Utility function to determine announcement status based on dates
const getAnnouncementStatus = (startDate, startTime, endDate, endTime, manuallyExpired = false) => {
    if (!startDate || !startTime || !endDate || !endTime) {
        return 'draft';
    }

    const now = new Date();
    const startDateTime = new Date(`${startDate} ${startTime}`);
    const endDateTime = new Date(`${endDate} ${endTime}`);

    // If manually expired and the actual end time hasn't passed yet, keep it expired
    if (manuallyExpired && now <= endDateTime) {
        return 'expired';
    }

    // Otherwise, use normal date-based logic
    if (now < startDateTime) {
        return 'upcoming';
    } else if (now >= startDateTime && now <= endDateTime) {
        return 'ongoing';
    } else {
        return 'expired';
    }
};



const AnnouncementList = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeTab, setActiveTab] = useState(() => {
        // Try to get the active tab from localStorage, fallback to location state or default to 1
        return parseInt(localStorage.getItem('announcementActiveTab')) || location.state?.activeTab || 1;
    });
    const [myPostChecked, setMyPostChecked] = useState(false);
    const [selectedPriority, setSelectedPriority] = useState("");
    const [selectedLabel, setSelectedLabel] = useState("");
    const [searchTerm, setSearchTerm] = useState("");
    const [announcements, setAnnouncements] = useState([]);
    const [loading, setLoading] = useState(false);
    const [availableLabels, setAvailableLabels] = useState([]);
    const [selectedImages, setSelectedImages] = useState([]);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const [isImageSliderOpen, setIsImageSliderOpen] = useState(false);
    const [openDropdownId, setOpenDropdownId] = useState(null);
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
    const [isFilterExpanded, setIsFilterExpanded] = useState(false);
    const [selectedDate, setSelectedDate] = useState("");
    const [announcementToDelete, setAnnouncementToDelete] = useState(null);
    const [showHistoryModal, setShowHistoryModal] = useState(false);
    const [selectedAnnouncementForHistory, setSelectedAnnouncementForHistory] = useState(null);
    const [selectedDocument, setSelectedDocument] = useState(null);
    const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Initialize pin post functionality
    const pinPost = usePinPost({
        announcements,
        setAnnouncements,
        onPinSuccess: (message) => {
            console.log('Pin success:', message);
            // You can add a toast notification here if needed
        },
        onPinError: (message) => {
            console.error('Pin error:', message);
            // You can add error notification here if needed
        }
    });

    // Load announcements from API
    useEffect(() => {
        loadAnnouncements();
    }, [myPostChecked, selectedPriority, selectedLabel, searchTerm]);

    // Set up interval to update statuses
    useEffect(() => {
        // Set up interval to check for status changes every minute
        const interval = setInterval(() => {
            updateAnnouncementStatuses();
        }, 60000); // Check every minute

        return () => clearInterval(interval);
    }, []);

    // Handle click outside dropdown
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setOpenDropdownId(null);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Handle location state changes (when returning from edit)
    useEffect(() => {
        if (location.state?.activeTab) {
            setActiveTab(location.state.activeTab);
            // Clear the state to prevent it from persisting on subsequent navigations
            window.history.replaceState({}, document.title);
        }

        // If returning from edit, refresh announcements to get updated data including history
        if (location.state?.announcementId) {
            console.log('Returning from edit, refreshing announcements...');
            loadAnnouncements();
            // Clear the state to prevent it from persisting on subsequent navigations
            window.history.replaceState({}, document.title);
        }
    }, [location.state]);

    // Load announcements from API
    const loadAnnouncements = async () => {
        setLoading(true);
        try {
            // Check authentication first
            const accessToken = localStorage.getItem('access_token');
            const member = localStorage.getItem('member');

            console.log('Authentication check:', {
                hasAccessToken: !!accessToken,
                hasMember: !!member,
                tokenLength: accessToken ? accessToken.length : 0
            });

            if (!accessToken) {
                console.error('No access token found. User needs to login.');
                setAnnouncements([]);
                setLoading(false);
                // You might want to redirect to login or show a login prompt here
                return;
            }

            // Get current user for filtering
            const currentUser = member ? JSON.parse(member) : null;

            // Prepare API parameters (My Posts filtering is handled on frontend)
            const params = {};
            if (selectedPriority) {
                params.priority = selectedPriority.toLowerCase();
            }
            if (selectedLabel) {
                params.label = selectedLabel;
            }
            if (searchTerm) {
                params.search = searchTerm;
            }

            console.log('Making API call with params:', params);
            const response = await announcementApi.getAnnouncements(params);

            // Debug: Log the API response
            console.log('API Response:', response);

            // Handle both paginated and direct array responses
            const announcementsData = response.results || response;
            console.log('Announcements data:', announcementsData);
            console.log('Announcements count:', Array.isArray(announcementsData) ? announcementsData.length : 'Not an array');

            // Transform API response to match frontend structure
            const transformedAnnouncements = Array.isArray(announcementsData) ? announcementsData.map(announcement => ({
                    id: announcement.id,
                    title: announcement.title,
                    description: announcement.description,
                    author: announcement.creator_name || 'Unknown Author',
                    creatorName: announcement.creator_name,
                    priority: announcement.priority,
                    label: announcement.label || '',
                startDate: announcement.start_date,
                startTime: announcement.start_time,
                endDate: announcement.end_date,
                endTime: announcement.end_time,
                status: announcement.status,
                views: announcement.views || 0,
                // Fix: Use 'pinned' property to match sorting function expectations
                pinned: announcement.is_pinned || false,
                // Keep isPinned for backward compatibility
                isPinned: announcement.is_pinned || false,
                manuallyExpired: announcement.manually_expired || false,
                createdAt: announcement.created_at,
                updatedAt: announcement.updated_at,
                attachments: announcement.attachments || [],
                postAs: announcement.post_as || 'creator',
                postedGroupName: announcement.group_name,
                postedMemberName: announcement.member_name,
                // Transform backend history to frontend editHistory format
                editHistory: announcement.history ? announcement.history.map(historyEntry => ({
                    editedBy: historyEntry.edited_by_name || 'Unknown User',
                    timestamp: historyEntry.edited_at,
                    changes: historyEntry.changes || {}
                })) : []
            })) : [];

            console.log('About to set announcements:', transformedAnnouncements.length);
            setAnnouncements(transformedAnnouncements);

            // Debug: Log status distribution
            const statusCounts = transformedAnnouncements.reduce((acc, ann) => {
                acc[ann.status] = (acc[ann.status] || 0) + 1;
                return acc;
            }, {});
            console.log('Status distribution:', statusCounts);
            console.log('Sample announcement:', transformedAnnouncements[0]);
            console.log('Sample announcement attachments:', transformedAnnouncements[0]?.attachments);

            // Check if any announcements have attachments
            const withAttachments = transformedAnnouncements.filter(a => a.attachments && a.attachments.length > 0);
            console.log('Announcements with attachments:', withAttachments.length);
            if (withAttachments.length > 0) {
                console.log('First announcement with attachments:', withAttachments[0]);
            }

            // Extract unique labels for the dropdown
            const uniqueLabels = [...new Set(
                transformedAnnouncements
                    .map(ann => ann.label)
                    .filter(label => label && label.trim() !== '')
            )];
            setAvailableLabels(uniqueLabels);
        } catch (error) {
            console.error('Error loading announcements:', error);
            console.error('Error details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });

            // Check if it's an authentication error
            if (error.response?.status === 401) {
                console.error('Authentication failed. User needs to login again.');
                // Clear invalid tokens
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                // You might want to redirect to login page here
                setAnnouncements([]);
            } else {
                // For other errors, try fallback to localStorage
                setAnnouncements([]);
                try {
                    const savedAnnouncements = localStorage.getItem('announcements');
                    if (savedAnnouncements) {
                        const parsedAnnouncements = JSON.parse(savedAnnouncements);
                        setAnnouncements(parsedAnnouncements);
                        console.log('Loaded announcements from localStorage as fallback');
                    }
                } catch (localError) {
                    console.error('Error loading from localStorage:', localError);
                }
            }
        } finally {
            setLoading(false);
        }
    };

    // Update announcement statuses based on current time
    const updateAnnouncementStatuses = async () => {
        try {
            // Call API to update all statuses
            await announcementApi.updateStatuses();
            // Reload announcements to get updated statuses
            loadAnnouncements();
        } catch (error) {
            console.error('Error updating announcement statuses:', error);
            // Fallback to local status calculation
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    const newStatus = getAnnouncementStatus(
                        announcement.startDate,
                        announcement.startTime,
                        announcement.endDate,
                        announcement.endTime,
                        announcement.manuallyExpired
                    );

                    // Clear manuallyExpired flag if the announcement has naturally expired
                    const now = new Date();
                    const endDateTime = new Date(`${announcement.endDate} ${announcement.endTime}`);
                    const shouldClearManualFlag = announcement.manuallyExpired && now > endDateTime;

                    return {
                        ...announcement,
                        status: newStatus,
                        manuallyExpired: shouldClearManualFlag ? false : announcement.manuallyExpired
                    };
                });

                return updatedAnnouncements;
            });
        }
    };

    // Filter announcements based on active tab
    const getFilteredAnnouncements = () => {
        let filtered = announcements;

        // Debug: Log initial data
        console.log('getFilteredAnnouncements - Total announcements:', announcements.length);
        console.log('getFilteredAnnouncements - Active tab:', activeTab);
        console.log('getFilteredAnnouncements - Sample statuses:', announcements.slice(0, 3).map(a => `${a.id}: ${a.status}`));

        // Filter by tab
        if (activeTab === 1) {
            filtered = filtered.filter(ann => ann.status === 'ongoing');
            console.log('getFilteredAnnouncements - Ongoing filtered:', filtered.length);
        } else if (activeTab === 2) {
            filtered = filtered.filter(ann => ann.status === 'upcoming');
            console.log('getFilteredAnnouncements - Upcoming filtered:', filtered.length);
        } else if (activeTab === 3) {
            filtered = filtered.filter(ann => ann.status === 'expired');
            console.log('getFilteredAnnouncements - Expired filtered:', filtered.length);
        }

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(ann =>
                ann.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                ann.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                ann.author.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by selected date
        if (selectedDate) {
            filtered = filtered.filter(ann => {
                if (!ann.startDate || !ann.endDate) return false;

                const selectedDateObj = new Date(selectedDate);
                selectedDateObj.setHours(0, 0, 0, 0); // Normalize to start of day

                const announcementStartDate = new Date(ann.startDate);
                announcementStartDate.setHours(0, 0, 0, 0); // Normalize to start of day

                const announcementEndDate = new Date(ann.endDate);
                announcementEndDate.setHours(0, 0, 0, 0); // Normalize to start of day

                return selectedDateObj >= announcementStartDate && selectedDateObj <= announcementEndDate;
            });
        }

        // Filter by priority
        if (selectedPriority) {
            console.log('Filtering by priority:', selectedPriority);
            console.log('Available priorities:', announcements.map(ann => ann.priority));
            filtered = filtered.filter(ann =>
                ann.priority && ann.priority.toLowerCase() === selectedPriority.toLowerCase()
            );
        }

        // Filter by label
        if (selectedLabel) {
            console.log('Filtering by label:', selectedLabel);
            console.log('Available labels:', announcements.map(ann => ann.label));
            filtered = filtered.filter(ann =>
                ann.label && ann.label.toLowerCase() === selectedLabel.toLowerCase()
            );
        }

        // Filter by my posts
        if (myPostChecked) {
            // Get current user from localStorage
            const member = localStorage.getItem('member');
            let currentUserName = null;

            if (member) {
                try {
                    const currentUser = JSON.parse(member);
                    currentUserName = currentUser.full_name || currentUser.fullName || currentUser.name;
                } catch (error) {
                    console.error('Error parsing member data:', error);
                }
            }

            console.log('Filtering by my posts, current user:', currentUserName);
            console.log('Available creators:', announcements.map(ann => ann.creatorName));

            if (currentUserName) {
                filtered = filtered.filter(ann =>
                    ann.creatorName && ann.creatorName.toLowerCase() === currentUserName.toLowerCase()
                );
            } else {
                // If no current user found, show no results
                filtered = [];
                console.warn('No current user found for My Posts filter');
            }
        }

        // Use pin post component sorting functionality
        return pinPost.sortAnnouncementsWithPinned(filtered);
    };

    // Handle create announcement navigation
    const handleCreateAnnouncement = () => {
        navigate('/create-announcement', {
            state: { sourceTab: activeTab }
        });
    };

    // Handle move to expired
    const handleMoveToExpired = async (announcementId) => {
        try {
            await announcementApi.forceExpire(announcementId);
            // Reload announcements to get updated data
            loadAnnouncements();
        } catch (error) {
            console.error('Error moving announcement to expired:', error);
            // Fallback to local update
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        return {
                            ...announcement,
                            status: 'expired',
                            manuallyExpired: true,
                            expiredAt: new Date().toISOString()
                        };
                    }
                    return announcement;
                });

                return updatedAnnouncements;
            });
        }
    };

    // Handle view announcement
    const handleViewAnnouncement = async (announcementId) => {
        try {
            // Increment view count via API
            await announcementApi.incrementViews(announcementId);
            // Update local state
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        return {
                            ...announcement,
                            views: (announcement.views || 0) + 1
                        };
                    }
                    return announcement;
                });
                return updatedAnnouncements;
            });
        } catch (error) {
            console.error('Error incrementing views:', error);
        }

        // Navigate to announcement details (when implemented)
        // navigate(`/announcements/${announcementId}`);
        console.log('View announcement:', announcementId);
    };

    // Handle edit announcement
    const handleEditAnnouncement = (announcementId) => {
        // Navigate to edit page with current tab information
        navigate(`/edit-announcement/${announcementId}`, {
            state: { 
                sourceTab: activeTab,
                announcementId: announcementId
            }
        });
    };

    // Handle announcement history
    const handleAnnouncementHistory = async (announcementId) => {
        try {
            // Fetch the latest announcement data to ensure we have the most recent history
            const response = await announcementApi.getAnnouncement(announcementId);

            // Transform the single announcement data to match frontend structure
            const transformedAnnouncement = {
                id: response.id,
                title: response.title,
                description: response.description,
                author: response.creator_name || 'Unknown Author',
                creatorName: response.creator_name,
                priority: response.priority,
                label: response.label || '',
                startDate: response.start_date,
                startTime: response.start_time,
                endDate: response.end_date,
                endTime: response.end_time,
                status: response.status,
                views: response.views || 0,
                // Fix: Use 'pinned' property to match sorting function expectations
                pinned: response.is_pinned || false,
                // Keep isPinned for backward compatibility
                isPinned: response.is_pinned || false,
                manuallyExpired: response.manually_expired || false,
                createdAt: response.created_at,
                updatedAt: response.updated_at,
                attachments: response.attachments || [],
                postAs: response.post_as || 'creator',
                postedGroupName: response.group_name,
                postedMemberName: response.member_name,
                // Transform backend history to frontend editHistory format
                editHistory: response.history ? response.history.map(historyEntry => ({
                    editedBy: historyEntry.edited_by_name || 'Unknown User',
                    timestamp: historyEntry.edited_at,
                    changes: historyEntry.changes || {}
                })) : []
            };

            setSelectedAnnouncementForHistory(transformedAnnouncement);
            setShowHistoryModal(true);
        } catch (error) {
            console.error('Error fetching announcement for history:', error);
            // Fallback to existing announcement data
            const announcement = announcements.find(ann => ann.id === announcementId);
            if (announcement) {
                setSelectedAnnouncementForHistory(announcement);
                setShowHistoryModal(true);
            }
        }
    };

    // Handle reminder
    const handleReminder = (announcementId) => {
        // Implement reminder functionality
        console.log('Set reminder for announcement:', announcementId);
        // TODO: Add reminder logic
    };

    // Use pin post component functionality
    const handlePinPost = pinPost.handlePinPost;

    // Handle direct communication
    const handleDirectCommunication = (announcementId) => {
        // Implement direct communication functionality
        console.log('Start direct communication for announcement:', announcementId);
        // TODO: Add direct communication logic
    };

    // Handle delete announcement
    const handleDeleteAnnouncement = (announcementId) => {
        setAnnouncementToDelete(announcementId);
        setShowDeleteConfirmation(true);
    };

    // Handle confirm delete
    const handleConfirmDelete = async () => {
        if (announcementToDelete) {
            try {
                await announcementApi.deleteAnnouncement(announcementToDelete);
                // Remove from local state
                setAnnouncements(prevAnnouncements => {
                    const updatedAnnouncements = prevAnnouncements.filter(announcement =>
                        announcement.id !== announcementToDelete
                    );
                    return updatedAnnouncements;
                });
                console.log('Deleted announcement:', announcementToDelete);
            } catch (error) {
                console.error('Error deleting announcement:', error);
                // You might want to show an error message to the user here
            }
        }
        setShowDeleteConfirmation(false);
        setAnnouncementToDelete(null);
    };

    // Handle cancel delete
    const handleCancelDelete = () => {
        setShowDeleteConfirmation(false);
        setAnnouncementToDelete(null);
    };

    // Handle restore announcement (remove manual expiration)
    const handleRestoreAnnouncement = async (announcementId) => {
        try {
            const response = await announcementApi.restore(announcementId);
            // Update local state with the new status from API
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        return {
                            ...announcement,
                            status: response.status || announcement.status,
                            manuallyExpired: false,
                            expiredAt: null
                        };
                    }
                    return announcement;
                });
                return updatedAnnouncements;
            });
            console.log('Restored announcement:', announcementId);
        } catch (error) {
            console.error('Error restoring announcement:', error);
            // Fallback to local update
            setAnnouncements(prevAnnouncements => {
                const updatedAnnouncements = prevAnnouncements.map(announcement => {
                    if (announcement.id === announcementId) {
                        // Remove manual expiration and recalculate status
                        const newStatus = getAnnouncementStatus(
                            announcement.startDate,
                            announcement.startTime,
                            announcement.endDate,
                            announcement.endTime,
                            false // Remove manual expiration
                        );

                        return {
                            ...announcement,
                            status: newStatus,
                            manuallyExpired: false,
                            expiredAt: null
                        };
                    }
                    return announcement;
                });
                return updatedAnnouncements;
            });
        }
    };

    // Helper function to check if file is an image
    const isImage = (fileName) => {
        const extension = fileName?.split('.').pop()?.toLowerCase();
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension);
    };

    // Handle image click to open slider
    const handleImageClick = (attachment, announcement) => {
        console.log('=== IMAGE CLICK DEBUG ===');
        console.log('Clicked attachment:', attachment);
        console.log('Announcement:', announcement);

        // Get all attachments from the announcement
        const allAttachments = announcement.attachments || [];
        console.log('All attachments:', allAttachments);

        // Filter only image files for the slider
        const imageAttachments = allAttachments.filter(att =>
            isImage(att.file_name || att.name) || (!att.file_name && !att.name && !isDocument(att.file_name || att.name))
        );
        console.log('Image attachments:', imageAttachments);

        // Find the index of the clicked image within the filtered images
        const clickedIndex = imageAttachments.findIndex(img =>
            (img.file_url || img.url || img) === (attachment.file_url || attachment.url || attachment)
        );
        console.log('Clicked index:', clickedIndex);

        // Prepare images for the slider
        const formattedImages = imageAttachments.map((img, index) => ({
            src: img.file_url || img.url || img,
            alt: img.file_name || img.name || `Image ${index + 1}`,
            name: img.file_name || img.name || `image-${index + 1}`
        }));
        console.log('Formatted images for slider:', formattedImages);

        setSelectedImages(formattedImages);
        setSelectedImageIndex(clickedIndex >= 0 ? clickedIndex : 0);
        setIsImageSliderOpen(true);
        console.log('Image slider should now be open');
    };

    // Handle image slider close
    const handleImageSliderClose = () => {
        setIsImageSliderOpen(false);
        setSelectedImages([]);
        setSelectedImageIndex(0);
    };

    // Handle document click to open viewer
    const handleDocumentClick = (attachment) => {
        console.log('=== DOCUMENT CLICK DEBUG ===');
        console.log('Clicked attachment:', attachment);
        console.log('File URL:', attachment.file_url || attachment.url || attachment.file);
        console.log('File name:', attachment.file_name || attachment.name);
        console.log('File type:', attachment.file_type || attachment.type);

        // For all documents (including PDFs), show the viewer modal
        setSelectedDocument(attachment);
        setIsDocumentViewerOpen(true);
        console.log('Document viewer should now be open');
    };

    // Handle document viewer close
    const handleDocumentClose = () => {
        setIsDocumentViewerOpen(false);
        setSelectedDocument(null);
    };

    // Helper function to get file icon
    const getFileIcon = (fileName) => {
        const extension = fileName?.split('.').pop()?.toLowerCase();
        switch (extension) {
            case 'pdf':
                return <FaFilePdf className="w-6 h-6 text-black font-bold" />;
            case 'doc':
            case 'docx':
                return <FaFileWord className="w-6 h-6 text-blue-500" />;
            default:
                return <FaImage className="w-6 h-6 text-gray-400" />;
        }
    };

    // Helper function to check if file is a document
    const isDocument = (fileName) => {
        const extension = fileName?.split('.').pop()?.toLowerCase();
        return ['pdf', 'doc', 'docx'].includes(extension);
    };

    // Handle history modal close
    const handleHistoryModalClose = () => {
        setShowHistoryModal(false);
        setSelectedAnnouncementForHistory(null);
    };

    // Handle dropdown toggle
    const handleDropdownToggle = (announcementId) => {
        setOpenDropdownId(openDropdownId === announcementId ? null : announcementId);
    };

    // Handle clear filters
    const handleClearFilters = () => {
        setSelectedPriority('');
        setSelectedLabel('');
        setMyPostChecked(false);
        setSearchTerm('');
        setSelectedDate('');
    };

    // Handle filter toggle
    const handleFilterToggle = () => {
        setIsFilterExpanded(!isFilterExpanded);
    };



    // Debug function to log announcement data
    const debugAnnouncements = () => {
        console.log('All announcements:', announcements);
        console.log('Filtered announcements:', getFilteredAnnouncements());
        console.log('Current filters:', {
            selectedPriority,
            selectedLabel,
            myPostChecked,
            searchTerm,
            activeTab
        });
    };

    // Add sample data for testing
    const handleAddSampleData = () => {
        const sampleAnnouncement = addSampleAnnouncementToStorage();
        loadAnnouncements(); // Reload to show the new sample
        console.log('Sample announcement added:', sampleAnnouncement);
    };

    // Handle tab change
    const handleTabChange = (tabNumber) => {
        setActiveTab(tabNumber);
        localStorage.setItem('announcementActiveTab', tabNumber.toString());
    };

    return (
        <div className="min-h-screen bg-gray-50 p-4">
            {/* Header */}
            <div className="mb-6">
                <div className="space-y-4">
                    {/* Main Header Row */}
                    <div className="flex items-center justify-between">
                        <h1 className="text-xl font-semibold text-gray-900">Announcements List</h1>

                        <div className="flex items-center space-x-4">
                            {/* My Post Checkbox */}
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={myPostChecked}
                                    onChange={(e) => setMyPostChecked(e.target.checked)}
                                    className="form-checkbox h-4 w-4 text-[#3D9D9B] rounded border-gray-300 focus:ring-[#3D9D9B]"
                                />
                                <span className="ml-2 text-sm text-[#3D9D9B]">
                                    My Post
                                </span>
                            </label>

                            {/* Filter Button */}
                            <button
                                onClick={handleFilterToggle}
                                className={`flex items-center px-4 py-2 rounded-lg transition-colors text-sm font-medium ${
                                    isFilterExpanded
                                        ? 'bg-[#3D9D9B] text-white'
                                        : 'bg-white text-[#3D9D9B] border border-[#3D9D9B] hover:bg-gray-50'
                                }`}
                            >
                                <BiFilter className="mr-2 w-4 h-4" />
                                Filter
                            </button>

                            {/* Create Announcements Button */}
                            <button
                                onClick={handleCreateAnnouncement}
                                className="flex items-center bg-[#3D9D9B] text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium"
                            >
                                <FaPlus className="mr-2 w-4 h-4" />
                                Create Announcements
                            </button>
                        </div>
                    </div>

                    {/* Expanded Filter Section - Separate Row */}
                    {isFilterExpanded && (
                        <div className="flex items-center justify-end space-x-6">
                            {/* Select Date */}
                            <div className="min-w-[160px]">
                               
                                <Calendar 
                                     value={selectedDate}
                                     onChange={setSelectedDate}
                                     placeholder="Select Date"
                                />
                            </div>

                            {/* Priority Dropdown */}
                            <div className="relative min-w-[160px]">
                                <select
                                    value={selectedPriority}
                                    onChange={(e) => setSelectedPriority(e.target.value)}
                                    className="w-full appearance-none bg-white border border-[#3D9D9B] rounded-lg px-4 py-2 pr-10 text-sm text-[#3D9D9B] focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                                >
                                    <option value="" className="text-[#3D9D9B]">Select Priority</option>
                                    <option value="Urgent">Urgent</option>
                                    <option value="High">High</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Low">Low</option>
                                </select>
                                <MdKeyboardArrowDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#3D9D9B] pointer-events-none w-4 h-4" />
                            </div>

                            {/* Label Dropdown */}
                            <div className="relative min-w-[160px]">
                                <select
                                    value={selectedLabel}
                                    onChange={(e) => setSelectedLabel(e.target.value)}
                                    className="w-full appearance-none bg-white border border-[#3D9D9B] rounded-lg px-4 py-2 pr-10 text-sm text-[#3D9D9B] focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
                                >
                                    <option value="" className="text-[#3D9D9B]">Select Label</option>
                                    {availableLabels.map((label, index) => (
                                        <option key={index} value={label}>{label}</option>
                                    ))}
                                </select>
                                <MdKeyboardArrowDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#3D9D9B] pointer-events-none w-4 h-4" />
                            </div>

                            {/* Search Input */}
                            <div className="relative flex-1 max-w-[300px]">
                                <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#3D9D9B] w-4 h-4" />
                                <input
                                    type="text"
                                    placeholder="Search list..."
                                    className="w-full pl-10 pr-4 py-2 border border-[#3D9D9B] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent text-sm text-[#3D9D9B] placeholder-[#3D9D9B]"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="rounded-[27px] bg-[#FFFFFF]">
                {/* Tabs */}
                <div className="p-4">
                    <div className="flex mb-4 bg-[#3C9D9B1A] rounded">
                        <button
                            className={`flex-1 w-full py-2 font-[600] text-[#090909] ${activeTab === 1 ? "border border-[#3D9D9B] bg-white" : "border border-transparent"}`}
                            onClick={() => handleTabChange(1)}
                        >
                            Ongoing
                        </button>
                        <button
                            className={`flex-1 w-full py-2 font-[600] text-[#090909] ${activeTab === 2 ? "border border-[#3D9D9B] bg-white" : "border border-transparent"}`}
                            onClick={() => handleTabChange(2)}
                        >
                            Upcoming
                        </button>
                        <button
                            className={`flex-1 w-full py-2 font-[600] text-[#090909] ${activeTab === 3 ? "border border-[#3D9D9B] bg-white" : "border border-transparent"}`}
                            onClick={() => handleTabChange(3)}
                        >
                            Expired
                        </button>
                    </div>



                    {/* Announcements List */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {loading ? (
                            <div className="col-span-full flex justify-center items-center py-12">
                                <div className="text-center">
                                    <div className="w-8 h-8 border-4 border-[#3D9D9B] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                    <p className="text-gray-500">Loading announcements...</p>
                                </div>
                            </div>
                        ) : getFilteredAnnouncements().length > 0 ? (
                            getFilteredAnnouncements().map((announcement) => (
                                <div key={announcement.id} className="bg-white border border-[#3D9D9B] rounded-lg p-4 shadow-sm">
                                    {/* Header */}
                                    <div className="flex justify-between items-start mb-3">
                                        <div className="flex items-center space-x-2">
                                            <div className="w-[24px] h-[24px] rounded-full flex items-center justify-center">
                                                {announcement.postAs === 'creator' ? (
                                                   <HiUserCircle className="w-8 h-8" color="gray" />
                                                ) : (
                                                    <FaUserGroup className="w-8 h-8" color="gray" />
                                                )}
                                            </div>
                                            <div>
                                                <h3 className="text-black text-[14px] font-bold">
                                                    {announcement.postAs === 'group' ? announcement.postedGroupName : 
                                                     announcement.postAs === 'member' ? announcement.postedMemberName :
                                                     announcement.author}
                                                </h3>
                                                <p className="text-black text-[11px] font-bold">
                                                    Creator {announcement.creatorName}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {/* Pin Icon */}
                                            <PinIcon announcement={announcement} />
                                            {/* Priority Flag */}
                                            {announcement.priority && (
                                                <FaFlag className={`w-[16px] h-[16px] ${getPriorityColor(announcement.priority)}`} />
                                            )}
                                             <div className="flex items-center ">
                                                <IoIosNotificationsOutline className="w-[18px] h-[18px] mr-1 text-gray-700" />
                                                <span className="text-[12px]">{announcement.views}</span>
                                            </div>
                                            <div className="relative" ref={openDropdownId === announcement.id ? dropdownRef : null}>
                                                <HiDotsHorizontal
                                                    className="w-[16px] h-[16px] text-[#3D9D9B] cursor-pointer hover:text-[#2A7A78]"
                                                    onClick={() => handleDropdownToggle(announcement.id)}
                                                />
                                                {openDropdownId === announcement.id && (
                                                    <AnnouncementActionMenu
                                                        announcement={announcement}
                                                        onEdit={handleEditAnnouncement}
                                                        onHistory={handleAnnouncementHistory}
                                                        onMoveToExpired={handleMoveToExpired}
                                                        onReminder={handleReminder}
                                                        onPinPost={handlePinPost}
                                                        onDirectCommunication={handleDirectCommunication}
                                                        onDelete={handleDeleteAnnouncement}
                                                        onRestore={handleRestoreAnnouncement}
                                                        onClose={() => setOpenDropdownId(null)}
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Date */}
                                    <div className="text-[12px] mb-2 flex items-center space-x-2">
                                        <span className="text-[#3D9D9B] font-bold">
                                            Start: {(() => {
                                                const date = new Date(announcement.startDate);
                                                const day = date.getDate().toString().padStart(2, '0');
                                                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                                                const year = date.getFullYear();
                                                const time = announcement.startTime || '';
                                                const [hours, minutes] = time.split(':');
                                                const hour24 = parseInt(hours);
                                                const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                                                const period = hour24 >= 12 ? 'pm' : 'am';
                                                return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                                            })()}
                                        </span>
                                        <span className="text-[#FF8682] font-bold">
                                            Expire: {(() => {
                                                const date = new Date(announcement.endDate);
                                                const day = date.getDate().toString().padStart(2, '0');
                                                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                                                const year = date.getFullYear();
                                                const time = announcement.endTime || '';
                                                const [hours, minutes] = time.split(':');
                                                const hour24 = parseInt(hours);
                                                const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
                                                const period = hour24 >= 12 ? 'pm' : 'am';
                                                return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
                                            })()}
                                        </span>
                                    </div>

                                    {/* Label - Next line */}
                                    {announcement.label && (
                                        <div className="text-[12px] mb-2">
                                            <div className="flex flex-wrap gap-1">
                                                {announcement.label.split(',').map((label, index) => (
                                                    <span
                                                        key={index}
                                                        className="bg-[#F5F5F5] text-black text-[10px] px-2 py-1 rounded font-bold"
                                                    >
                                                        {label.trim()}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {/* Title */}
                                    <h4 className="text-[#000] text-[14px] font-semibold mb-2 line-clamp-2">
                                        {announcement.title}
                                    </h4>

                                    {/* Description */}
                                    <p className="text-[#666] text-[12px] mb-3">
                                        {announcement.description}
                                    </p>

                                    {/* Attachments - Show by default */}
                                    <div className="mb-3">
                                        <div className="space-y-2">
                                            {/* Show actual images if they exist, otherwise show placeholder */}
                                            {announcement.attachments && announcement.attachments.length > 0 ? (
                                                (() => {
                                                    console.log(`Announcement ${announcement.id} attachments:`, announcement.attachments);

                                                    const totalAttachments = announcement.attachments.length;

                                                    return (
                                                        <div className="space-y-2">
                                                            {/* Main/First Image/PDF - Display prominently */}
                                                            <div
                                                                key={announcement.attachments[0].id || 0}
                                                                className="relative bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200"
                                                                style={{
                                                                    width: '316px',
                                                                    height: '243px',
                                                                    borderRadius: '8px'
                                                                }}
                                                                onClick={() => isDocument(announcement.attachments[0].file_name || announcement.attachments[0].name) ? handleDocumentClick(announcement.attachments[0]) : handleImageClick(announcement.attachments[0], announcement)}
                                                            >
                                                                {isDocument(announcement.attachments[0].file_name || announcement.attachments[0].name) ? (
                                                                    <div className="w-full h-full flex items-center justify-center">
                                                                        <div className="flex items-center">
                                                                            {getFileIcon(announcement.attachments[0].file_name || announcement.attachments[0].name)}
                                                                            <div className="ml-2 text-left">
                                                                                <div className="text-sm font-medium text-gray-900">
                                                                                    {(announcement.attachments[0].file_name || announcement.attachments[0].name)?.toLowerCase().endsWith('.pdf') ? 'PDF Document' : 'Word Document'}
                                                                                </div>
                                                                                <div className="text-xs text-gray-500">{announcement.attachments[0].file_name || announcement.attachments[0].name}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                ) : (
                                                                    <img
                                                                        src={announcement.attachments[0].file_url || announcement.attachments[0].url || announcement.attachments[0]}
                                                                        alt={announcement.attachments[0].file_name || announcement.attachments[0].name || `Attachment 1`}
                                                                        className="w-full h-full object-cover"
                                                                        onError={(e) => {
                                                                            console.error('Image load error for:', announcement.attachments[0].file_url);
                                                                            e.target.style.display = 'none';
                                                                        }}
                                                                    />
                                                                )}
                                                            </div>

                                                            {/* Additional Images/Files - Show as thumbnails if more than 1 */}
                                                            {totalAttachments > 1 && (
                                                                <div className="flex gap-2 overflow-x-auto">
                                                                    {announcement.attachments.slice(1).map((attachment, index) => (
                                                                        <div
                                                                            key={attachment.id || (index + 1)}
                                                                            className="relative flex-shrink-0 bg-gray-100 overflow-hidden cursor-pointer hover:opacity-90 transition-all duration-200 border border-gray-200"
                                                                            style={{
                                                                                width: '75px',
                                                                                height: '57.1px',
                                                                                borderRadius: '8px',
                                                                                transform: 'rotate(-180deg)'
                                                                            }}
                                                                            onClick={() => isDocument(attachment.file_name || attachment.name) ? handleDocumentClick(attachment) : handleImageClick(attachment, announcement)}
                                                                        >
                                                                            {isDocument(attachment.file_name || attachment.name) ? (
                                                                                <div className="w-full h-full flex items-center justify-center">
                                                                                    <div className="scale-75">
                                                                                        {getFileIcon(attachment.file_name || attachment.name)}
                                                                                    </div>
                                                                                </div>
                                                                            ) : (
                                                                                <img
                                                                                    src={attachment.file_url || attachment.url || attachment}
                                                                                    alt={attachment.file_name || attachment.name || `Attachment ${index + 2}`}
                                                                                    className="w-full h-full object-cover"
                                                                                    onError={(e) => {
                                                                                        console.error('Image load error for:', attachment.file_url);
                                                                                        e.target.style.display = 'none';
                                                                                    }}
                                                                                />
                                                                            )}
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                })()
                                            ) : (
                                                // Show placeholder images by default
                                                <div className="space-y-2">
                                                    <div className="bg-gray-100 border flex items-center justify-center" style={{ width: '316px', height: '243px', borderRadius: '8px' }}>
                                                        <FaImage className="w-8 h-8 text-gray-400" />
                                                    </div>
                                                    <div className="flex gap-2">
                                                        {Array.from({ length: 3 }).map((_, index) => (
                                                            <div key={index} className="bg-gray-100 border flex items-center justify-center" style={{ width: '75px', height: '57.1px', borderRadius: '8px', transform: 'rotate(-180deg)' }}>
                                                                <FaImage className="w-4 h-4 text-gray-400" />
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>


                                </div>
                            ))
                        ) : (
                            <div className="col-span-full flex flex-col items-center justify-center py-12">
                                <div className="text-center">
                                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                                        <FaFlag className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements found</h3>
                                    <p className="text-gray-500 mb-4">
                                        {!localStorage.getItem('access_token') ? (
                                            "Please log in to view announcements."
                                        ) : (
                                            <>
                                                {activeTab === 1 && "There are no ongoing announcements at the moment."}
                                                {activeTab === 2 && "There are no upcoming announcements at the moment."}
                                                {activeTab === 3 && "There are no expired announcements at the moment."}
                                                {activeTab === 0 && "There are no announcements at the moment."}
                                            </>
                                        )}
                                    </p>
                                    {!localStorage.getItem('access_token') && (
                                        <button
                                            onClick={() => window.location.href = '/login'}
                                            className="px-4 py-2 bg-[#3D9D9B] text-white rounded-lg hover:bg-[#2d7a78] transition-colors"
                                        >
                                            Go to Login
                                        </button>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Image Slider Modal */}
            <ImageSlider
                isOpen={isImageSliderOpen}
                onClose={handleImageSliderClose}
                images={selectedImages}
                initialIndex={selectedImageIndex}
            />

            {/* Delete Confirmation Modal */}
            {showDeleteConfirmation && (
                <ConfirmationMessageBox
                    message="Are you sure you want to delete this announcement? This action cannot be undone."
                    onConfirm={handleConfirmDelete}
                    onCancel={handleCancelDelete}
                />
            )}

            {/* History Modal */}
            <AnnouncementHistoryModal
                isOpen={showHistoryModal}
                onClose={handleHistoryModalClose}
                announcement={selectedAnnouncementForHistory}
                currentUser={localStorage.getItem('currentUser') || 'Current User'}
            />

            {/* Document Viewer */}
            {isDocumentViewerOpen && selectedDocument && (
                <DocumentViewer
                    fileUrl={selectedDocument.file_url || selectedDocument.url || selectedDocument.base64}
                    fileName={selectedDocument.file_name || selectedDocument.name}
                    onClose={handleDocumentClose}
                />
            )}
        </div>
    );
};

export default AnnouncementList;
